OM PRAKASH SONKAR
Team Lead - AI Engineer

Phone: +91-**********
Email: <EMAIL>
LinkedIn: https://www.linkedin.com/in/om-prakash-sonkar-1a0a79114/
GitHub: https://github.com/ommnnitald
Location: India

PROFESSIONAL SUMMARY

Results-driven Team Lead and AI Engineer with 3+ years of experience leading cross-functional teams to deliver production-ready ML/LLM systems. Proven track record of architecting AI solutions with 95% accuracy for healthcare, regulatory compliance, and predictive maintenance applications. Expert in GenAI technologies, prompt engineering, and scaling AI systems from prototype to deployment. Successfully managed teams of 5+ engineers while maintaining strong client relationships and delivering projects on time.

TECHNICAL SKILLS

Programming Languages: Python, SQL
Machine Learning & Deep Learning: Scikit-learn, TensorFlow, PyTorch, Keras, Predictive Analytics
Generative AI & NLP: HuggingFace, OpenAI APIs, LangChain, Prompt Engineering, ChatGPT, Gemini, Natural Language Processing
Computer Vision: Image Processing, Video Analysis, Facial Recognition
Development Tools: Git, Linux, Flask, PM2, REST APIs, Jupyter, VSCode
Leadership & Soft Skills: Technical Leadership, Client Communication, Agile Methodology, Team Management, Cross-functional Collaboration

WORK EXPERIENCE

Team Lead - AI Engineer
Current Role | 2022 - Present

• Lead a team of 5 engineers in developing and deploying client-facing ML/LLM systems, resulting in 100% on-time project delivery
• Architected facial-video-based vital monitoring AI system achieving 95% accuracy for healthcare applications
• Designed and implemented AI-powered BRSR Reporting Assistant for regulatory compliance, reducing manual effort by 80%
• Developed biometric voice authentication system using HuggingFace transformers, improving security protocols by 60%
• Managed end-to-end delivery of ONGC offshore AI solution for predictive maintenance and survival analytics
• Established technical standards and code review processes, improving team productivity by 40%

AI Engineer
Previous Role | 2021 - 2022

• Built and deployed machine learning models for predictive analytics and computer vision applications
• Collaborated with cross-functional teams to translate business requirements into technical solutions
• Implemented REST APIs and microservices architecture for ML model deployment
• Conducted data analysis and feature engineering to improve model performance by 25%
• Mentored junior developers and contributed to technical documentation and best practices

EDUCATION

Bachelor of Technology in Production & Industrial Engineering
National Institute of Technology (NIT), Allahabad
Graduated: December 2020
CGPA: 6.1/10

CERTIFICATIONS

Complete Python Bootcamp - Udemy
Complete ML & Data Science Bootcamp - Udemy
SaMD ISO 13485 Certification for Medical AI System Validation

KEY PROJECTS

AI-Powered BRSR Reporting Assistant
Developed regulatory compliance automation system using LangChain and OpenAI APIs, reducing report generation time by 75%

Facial-Video-Based Vital Monitoring System
Architected computer vision solution for healthcare monitoring with 95% accuracy, deployed in production environment

Biometric Voice Authentication System
Built secure authentication system using HuggingFace transformers and deep learning techniques

ONGC Offshore AI Solution
Delivered predictive maintenance and survival analytics system for offshore operations, improving safety protocols

2024 Indian Election Sentiment Analyzer
Created real-time sentiment analysis system with 85% accuracy match to actual election outcomes

Medical AI System Validation
Led SaMD ISO 13485 certification process for medical AI system, ensuring regulatory compliance

Industrial IoT Predictive Maintenance
Developed ML models for equipment failure prediction, reducing downtime by 30%

Cross-Platform ML Pipeline
Built automated ML pipeline for model training, validation, and deployment across multiple environments

ACHIEVEMENTS

NSS Graduate - MNNIT Allahabad
Student Honor Certificate - District Magistrate
Industrial Training Completion - Obra Thermal Power Plant
Successfully led team delivery of 10+ AI/ML projects with 100% client satisfaction
Established technical mentorship program resulting in 50% faster onboarding for new team members
