# PDF Resume Files - <PERSON><PERSON> <PERSON>

## Available PDF Files

### 1. **<PERSON><PERSON>_<PERSON>_<PERSON>_LinkedIn_Enhanced_Resume_2025.pdf** ✅ **[LATEST & RECOMMENDED]**
- **Size**: 49KB
- **Target Roles**: Team Lead AI, AI Engineering Manager, Senior AI Engineer
- **Focus**: Leadership, team management, project delivery
- **Current Role**: Team Lead AI Engineer at AIVOT AI (Sep 2024 - Present)
- **Certifications**: 10 LinkedIn Learning certifications (replaced Udemy courses)
- **Best For**: All AI/ML positions, maximum credibility and ATS optimization
- **Status**: ✅ **Enhanced with LinkedIn Learning certifications - RECOMMENDED**

### 2. **O<PERSON>_<PERSON>_Sonkar_LLM_Developer.pdf** ✅
- **Size**: 45KB
- **Target Roles**: <PERSON><PERSON> Developer, NLP Engineer, GenAI Engineer
- **Focus**: Large Language Models, prompt engineering, transformer models
- **Best For**: Companies working with LLMs, GenAI startups, NLP-focused roles
- **Status**: ✅ Ready for download and application

### 3. **Previous Versions** (Archived)
- **<PERSON><PERSON>_<PERSON>_<PERSON>kar_TeamLead_AI_Resume_2025_Updated.pdf** (48KB) - Before LinkedIn Learning enhancement
- **Om_Prakash_Sonkar_TeamLead_AI_Resume_2025.pdf** (45KB) - Original version

## File Locations

All PDF files are located in:
```
/home/<USER>/Documents/augment-projects/resume/
```

## How to Use These PDFs

### For Job Applications:
1. **Download** the appropriate PDF version based on the target role
2. **Review** the content to ensure it matches the job requirements
3. **Customize** if needed by editing the source files and regenerating
4. **Submit** through company portals or email

### For Different Job Types:

**🎯 Team Lead / Management Roles**
→ Use: `Om_Prakash_Sonkar_TeamLead_AI_Resume_2025.pdf`

**🤖 LLM / GenAI Specialist Roles**  
→ Use: `Om_Prakash_Sonkar_LLM_Developer.pdf`

**📊 General AI/ML Positions**
→ Use: `Om_Prakash_Sonkar_TeamLead_AI_Resume_2025.pdf` (covers broad technical skills)

## PDF Features

✅ **ATS-Optimized**: Clean formatting, no tables or graphics
✅ **Professional Layout**: Standard fonts, proper spacing
✅ **Print-Ready**: High-quality formatting for physical printing
✅ **Digital-Friendly**: Optimized for online applications and email
✅ **Keyword-Rich**: Industry-relevant terms for ATS parsing

## File Specifications

- **Format**: PDF (Portable Document Format)
- **Compatibility**: All major ATS systems, email clients, browsers
- **Font**: Arial (web-safe, ATS-friendly)
- **Size**: ~45KB each (fast loading, email-friendly)
- **Pages**: Single page (concise, recruiter-friendly)

## Next Steps

1. **Download** the appropriate PDF file
2. **Test** by uploading to a few job portals to ensure proper parsing
3. **Keep** both versions handy for different application types
4. **Update** as needed by modifying source files and regenerating PDFs

---

**Last Updated**: August 5, 2025
**Generated By**: LibreOffice PDF Export
**Quality**: Production-ready for professional applications
