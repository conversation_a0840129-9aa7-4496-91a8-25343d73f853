OM PRAKASH SONKAR
LLM Developer & AI Engineer

Phone: +91-8299603748
Email: <EMAIL>
LinkedIn: https://www.linkedin.com/in/om-prakash-sonkar-1a0a79114/
GitHub: https://github.com/ommnnitald
Location: India

PROFESSIONAL SUMMARY

Experienced LLM Developer and AI Engineer with 3+ years specializing in Large Language Models, prompt engineering, and GenAI applications. Expert in HuggingFace transformers, OpenAI APIs, and LangChain with proven track record of deploying production-ready LLM systems. Led development of AI-powered regulatory compliance tools and biometric authentication systems. Strong background in fine-tuning models and optimizing LLM performance for enterprise applications.

TECHNICAL SKILLS

Programming Languages: Python, SQL
Large Language Models: HuggingFace Transformers, OpenAI GPT APIs, ChatGPT, Gemini
GenAI Frameworks: LangChain, Prompt Engineering, Fine-tuning, Model Optimization
Machine Learning: Scikit-learn, TensorFlow, PyTorch, Keras, Natural Language Processing
Development Tools: Git, Linux, Flask, PM2, REST APIs, Jupyter, VSCode
Specialized Skills: Prompt Engineering, Model Fine-tuning, RAG Implementation, Vector Databases

WORK EXPERIENCE

Team Lead - LLM Developer
Current Role
2022 - Present

- Led development of AI-powered BRSR Reporting Assistant using LangChain and OpenAI APIs, reducing report generation time by 75%
- Architected biometric voice authentication system using HuggingFace transformers, improving security protocols by 60%
- Implemented prompt engineering strategies for 2024 Indian Election Sentiment Analyzer achieving 85% accuracy
- Designed and deployed LLM-based solutions for regulatory compliance automation
- Fine-tuned transformer models for domain-specific applications in healthcare and finance
- Established best practices for prompt engineering and model evaluation across team of 5 engineers

LLM Engineer
Previous Role
2021 - 2022

- Built and optimized transformer-based models for natural language processing applications
- Implemented RAG (Retrieval-Augmented Generation) systems for knowledge-based AI assistants
- Developed custom tokenization and preprocessing pipelines for domain-specific text data
- Collaborated with data science teams to improve model performance through feature engineering
- Created automated evaluation frameworks for LLM output quality assessment

EDUCATION

Bachelor of Technology in Production and Industrial Engineering
National Institute of Technology NIT Allahabad
Graduated: December 2020
CGPA: 6.1/10

CERTIFICATIONS

LinkedIn Learning Certifications:
- Introduction to Large Language Models - LinkedIn Learning
- Introduction to Prompt Engineering for Generative AI - LinkedIn Learning
- Advanced Prompt Engineering Techniques - LinkedIn Learning
- What Is Generative AI? - LinkedIn Learning
- Practical GitHub Copilot - LinkedIn Learning
- Introduction to Artificial Intelligence - LinkedIn Learning
- Machine Learning with Python: Foundations - LinkedIn Learning
- Artificial Intelligence Foundations: Machine Learning - LinkedIn Learning
- Deep Learning: Getting Started - LinkedIn Learning
- Foundations of Responsible AI - LinkedIn Learning

Professional Certifications:
- SaMD ISO 13485 Certification for Medical AI System Validation

KEY PROJECTS

AI-Powered BRSR Reporting Assistant
Developed enterprise-grade regulatory compliance system using LangChain, OpenAI APIs, and custom prompt engineering

Biometric Voice Authentication with Transformers
Built secure authentication system using fine-tuned HuggingFace models for voice biometric analysis

LLM-Based Sentiment Analysis Engine
Created real-time sentiment analysis system for 2024 Indian Elections with 85% accuracy using transformer models

Medical AI Compliance System
Led development of SaMD ISO 13485 compliant AI system with natural language processing capabilities

Enterprise Chatbot with RAG
Implemented retrieval-augmented generation system for customer service automation

Multi-Modal AI Assistant
Developed AI assistant combining text, voice, and image processing using transformer architectures

Custom Model Fine-tuning Pipeline
Built automated pipeline for fine-tuning LLMs on domain-specific datasets

Prompt Engineering Framework
Established systematic approach to prompt optimization resulting in 40% improvement in model outputs

ACHIEVEMENTS

Successfully deployed 8+ LLM-based systems in production environments
Led prompt engineering initiatives resulting in 40% improvement in model accuracy
Established LLM evaluation frameworks adopted across organization
Expert in transformer model optimization and deployment strategies
