<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <title><PERSON><PERSON> - Resume</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.4;
            margin: 0.5in;
            font-size: 11pt;
            color: #000;
        }
        .header {
            text-align: center;
            margin-bottom: 20px;
        }
        .name {
            font-size: 18pt;
            font-weight: bold;
            margin-bottom: 5px;
        }
        .title {
            font-size: 12pt;
            margin-bottom: 10px;
        }
        .contact {
            font-size: 10pt;
            line-height: 1.2;
        }
        .section {
            margin-bottom: 15px;
        }
        .section-title {
            font-size: 12pt;
            font-weight: bold;
            text-transform: uppercase;
            border-bottom: 1px solid #000;
            margin-bottom: 8px;
            padding-bottom: 2px;
        }
        .job-title {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .job-details {
            font-style: italic;
            margin-bottom: 5px;
        }
        .bullet {
            margin-left: 15px;
            margin-bottom: 3px;
        }
        .project-title {
            font-weight: bold;
            margin-bottom: 2px;
        }
        .skills {
            margin-bottom: 5px;
        }
        .skills-category {
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="header">
        <div class="name">OM PRAKASH SONKAR</div>
        <div class="title">LLM Developer & AI Engineer</div>
        <div class="contact">
            Phone: +91-8299603748 | Email: <EMAIL><br>
            LinkedIn: https://www.linkedin.com/in/om-prakash-sonkar-1a0a79114/<br>
            GitHub: https://github.com/ommnnitald | Location: India
        </div>
    </div>

    <div class="section">
        <div class="section-title">Professional Summary</div>
        <p>Experienced LLM Developer and AI Engineer with 3+ years specializing in Large Language Models, prompt engineering, and GenAI applications. Expert in HuggingFace transformers, OpenAI APIs, and LangChain with proven track record of deploying production-ready LLM systems. Led development of AI-powered regulatory compliance tools and biometric authentication systems. Strong background in fine-tuning models and optimizing LLM performance for enterprise applications.</p>
    </div>

    <div class="section">
        <div class="section-title">Technical Skills</div>
        <div class="skills"><span class="skills-category">Programming Languages:</span> Python, SQL</div>
        <div class="skills"><span class="skills-category">Large Language Models:</span> HuggingFace Transformers, OpenAI GPT APIs, ChatGPT, Gemini</div>
        <div class="skills"><span class="skills-category">GenAI Frameworks:</span> LangChain, Prompt Engineering, Fine-tuning, Model Optimization</div>
        <div class="skills"><span class="skills-category">Machine Learning:</span> Scikit-learn, TensorFlow, PyTorch, Keras, Natural Language Processing</div>
        <div class="skills"><span class="skills-category">Development Tools:</span> Git, Linux, Flask, PM2, REST APIs, Jupyter, VSCode</div>
        <div class="skills"><span class="skills-category">Specialized Skills:</span> Prompt Engineering, Model Fine-tuning, RAG Implementation, Vector Databases</div>
    </div>

    <div class="section">
        <div class="section-title">Work Experience</div>
        
        <div class="job-title">Team Lead - AI Engineer</div>
        <div class="job-details">Current Role | 2022 - Present</div>
        <div class="bullet">• Lead a team of 5 engineers in developing and deploying client-facing ML/LLM systems, resulting in 100% on-time project delivery</div>
        <div class="bullet">• Architected facial-video-based vital monitoring AI system achieving 95% accuracy for healthcare applications</div>
        <div class="bullet">• Designed and implemented AI-powered BRSR Reporting Assistant for regulatory compliance, reducing manual effort by 80%</div>
        <div class="bullet">• Developed biometric voice authentication system using HuggingFace transformers, improving security protocols by 60%</div>
        <div class="bullet">• Managed end-to-end delivery of ONGC offshore AI solution for predictive maintenance and survival analytics</div>
        <div class="bullet">• Established technical standards and code review processes, improving team productivity by 40%</div>
        
        <br>
        <div class="job-title">AI Engineer</div>
        <div class="job-details">Previous Role | 2021 - 2022</div>
        <div class="bullet">• Built and deployed machine learning models for predictive analytics and computer vision applications</div>
        <div class="bullet">• Collaborated with cross-functional teams to translate business requirements into technical solutions</div>
        <div class="bullet">• Implemented REST APIs and microservices architecture for ML model deployment</div>
        <div class="bullet">• Conducted data analysis and feature engineering to improve model performance by 25%</div>
        <div class="bullet">• Mentored junior developers and contributed to technical documentation and best practices</div>
    </div>

    <div class="section">
        <div class="section-title">Education</div>
        <div class="job-title">Bachelor of Technology in Production & Industrial Engineering</div>
        <div class="job-details">National Institute of Technology (NIT), Allahabad | Graduated: December 2020 | CGPA: 6.1/10</div>
    </div>

    <div class="section">
        <div class="section-title">Certifications</div>
        <div class="bullet">• Complete Python Bootcamp - Udemy</div>
        <div class="bullet">• Complete ML & Data Science Bootcamp - Udemy</div>
        <div class="bullet">• SaMD ISO 13485 Certification for Medical AI System Validation</div>
    </div>

    <div class="section">
        <div class="section-title">Key Projects</div>
        
        <div class="project-title">AI-Powered BRSR Reporting Assistant</div>
        <div class="bullet">Developed regulatory compliance automation system using LangChain and OpenAI APIs, reducing report generation time by 75%</div>
        
        <div class="project-title">Facial-Video-Based Vital Monitoring System</div>
        <div class="bullet">Architected computer vision solution for healthcare monitoring with 95% accuracy, deployed in production environment</div>
        
        <div class="project-title">Biometric Voice Authentication System</div>
        <div class="bullet">Built secure authentication system using HuggingFace transformers and deep learning techniques</div>
        
        <div class="project-title">ONGC Offshore AI Solution</div>
        <div class="bullet">Delivered predictive maintenance and survival analytics system for offshore operations, improving safety protocols</div>
        
        <div class="project-title">2024 Indian Election Sentiment Analyzer</div>
        <div class="bullet">Created real-time sentiment analysis system with 85% accuracy match to actual election outcomes</div>
        
        <div class="project-title">Medical AI System Validation</div>
        <div class="bullet">Led SaMD ISO 13485 certification process for medical AI system, ensuring regulatory compliance</div>
    </div>

    <div class="section">
        <div class="section-title">Achievements</div>
        <div class="bullet">• NSS Graduate - MNNIT Allahabad</div>
        <div class="bullet">• Student Honor Certificate - District Magistrate</div>
        <div class="bullet">• Industrial Training Completion - Obra Thermal Power Plant</div>
        <div class="bullet">• Successfully led team delivery of 10+ AI/ML projects with 100% client satisfaction</div>
        <div class="bullet">• Established technical mentorship program resulting in 50% faster onboarding for new team members</div>
    </div>
</body>
</html>
