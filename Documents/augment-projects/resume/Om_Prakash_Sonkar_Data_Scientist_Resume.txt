OM PRAKASH SONKAR
Senior Data Scientist & AI Engineer

Phone: +91-**********
Email: <EMAIL>
LinkedIn: https://www.linkedin.com/in/om-prakash-sonkar-1a0a79114/
GitHub: https://github.com/ommnnitald
Location: India

PROFESSIONAL SUMMARY

Senior Data Scientist with 3+ years of experience in predictive analytics, statistical modeling, and AI-driven insights generation. Proven expertise in building end-to-end data science solutions from hypothesis to production deployment. Led cross-functional teams to deliver data-driven solutions achieving 95% accuracy in healthcare monitoring and 85% prediction accuracy in sentiment analysis. Strong background in statistical analysis, feature engineering, and translating business problems into actionable data science solutions.

TECHNICAL SKILLS

Programming Languages: Python, SQL
Statistical Analysis: Predictive Analytics, Statistical Modeling, Hypothesis Testing, A/B Testing
Machine Learning: Scikit-learn, TensorFlow, PyTorch, Keras, Feature Engineering, Model Selection
Data Science Tools: Jupyter, Pandas, NumPy, Matplotlib, Seaborn, Statistical Analysis
Advanced Analytics: Time Series Analysis, Survival Analytics, Sentiment Analysis, Computer Vision
Development Tools: Git, Linux, Flask, REST APIs, Model Deployment, MLOps

WORK EXPERIENCE

Senior Data Scientist & Team Lead
Current Role
2022 - Present

- Led data science initiatives for healthcare vital monitoring achieving 95% prediction accuracy through advanced statistical modeling
- Developed predictive maintenance models for ONGC offshore operations, reducing equipment downtime by 30%
- Built comprehensive sentiment analysis framework for 2024 Indian Elections with 85% real-world accuracy correlation
- Designed statistical models for regulatory compliance automation, improving efficiency by 80%
- Conducted survival analytics for offshore safety protocols, enhancing risk assessment capabilities
- Managed team of 5 data scientists and engineers, establishing best practices for model validation and deployment

Data Scientist
Previous Role
2021 - 2022

- Built predictive models for industrial equipment failure prediction using time series analysis and machine learning
- Conducted statistical analysis and hypothesis testing to validate model performance and business impact
- Implemented feature engineering pipelines improving model performance by 25%
- Collaborated with business stakeholders to translate requirements into data science solutions
- Developed automated reporting systems and data visualization dashboards for executive decision-making

EDUCATION

Bachelor of Technology in Production and Industrial Engineering
National Institute of Technology NIT Allahabad
Graduated: December 2020
CGPA: 6.1/10

CERTIFICATIONS

LinkedIn Learning Certifications:
- Machine Learning with Python: Foundations - LinkedIn Learning
- Artificial Intelligence Foundations: Machine Learning - LinkedIn Learning
- Deep Learning: Getting Started - LinkedIn Learning
- Introduction to Artificial Intelligence - LinkedIn Learning
- What Is Generative AI? - LinkedIn Learning
- Introduction to Large Language Models - LinkedIn Learning
- Foundations of Responsible AI - LinkedIn Learning
- Introduction to Prompt Engineering for Generative AI - LinkedIn Learning
- Advanced Prompt Engineering Techniques - LinkedIn Learning
- Practical GitHub Copilot - LinkedIn Learning

Professional Certifications:
- SaMD ISO 13485 Certification for Medical AI System Validation

KEY PROJECTS

Healthcare Vital Monitoring Predictive Model
Developed statistical models for facial-video-based vital sign prediction achieving 95% accuracy using computer vision and time series analysis

Predictive Maintenance Analytics for ONGC
Built comprehensive survival analysis and predictive maintenance models for offshore operations, improving safety and reducing costs

Election Sentiment Analysis with Statistical Validation
Created real-time sentiment analysis system with rigorous statistical validation achieving 85% correlation with actual outcomes

Industrial IoT Predictive Analytics
Developed time series forecasting models for equipment failure prediction, reducing unplanned downtime by 30%

Regulatory Compliance Data Analytics
Built automated data analysis pipeline for BRSR reporting, reducing manual analysis time by 75%

Statistical Model Validation Framework
Established comprehensive model validation and testing framework ensuring statistical rigor across all data science projects

Customer Behavior Analytics
Conducted advanced statistical analysis of customer behavior patterns, identifying key drivers of engagement

Business Intelligence Dashboard
Created executive-level analytics dashboard providing real-time insights into key business metrics

ACHIEVEMENTS

Led statistical validation of 10+ predictive models with proven business impact
Established data science best practices adopted across organization
Expert in translating complex statistical concepts into business-actionable insights
Successfully deployed predictive analytics solutions reducing operational costs by 25%
Published internal research on advanced statistical modeling techniques
