# O<PERSON> <PERSON> - Resume Collection

This repository contains multiple versions of <PERSON><PERSON> <PERSON>'s resume, each optimized for different AI/ML roles and ATS systems.

## Resume Versions

### 1. **<PERSON><PERSON>_<PERSON>_<PERSON>_TeamLead_AI_Resume_2025.md**
- **Target Roles**: Team Lead AI, AI Engineering Manager, Senior AI Engineer
- **Focus**: Leadership, team management, project delivery
- **Best For**: Management positions, senior roles requiring leadership experience

### 2. **<PERSON><PERSON>_<PERSON>_<PERSON>_ATS_Resume.txt**
- **Target Roles**: General AI Engineer, ML Engineer, Technical Lead
- **Focus**: Balanced technical and leadership skills
- **Best For**: Most AI/ML positions, general applications
- **Format**: Plain text, maximum ATS compatibility

### 3. **O<PERSON>_<PERSON>_Sonkar_LLM_Developer_Resume.txt**
- **Target Roles**: <PERSON><PERSON> Developer, NLP Engineer, GenAI Engineer
- **Focus**: Large Language Models, prompt engineering, transformer models
- **Best For**: Companies working with LLMs, GenAI startups, NLP-focused roles

### 4. **<PERSON><PERSON>_<PERSON>_<PERSON>_Data_Scientist_Resume.txt**
- **Target Roles**: Data Scientist, Senior Data Scientist, Analytics Lead
- **Focus**: Statistical analysis, predictive modeling, business insights
- **Best For**: Data science positions, analytics roles, research-oriented companies

## ATS Optimization Features

All resumes include:
- ✅ No tables, columns, or graphics
- ✅ Standard section headers
- ✅ Keyword optimization for AI/ML roles
- ✅ Quantified achievements and impact metrics
- ✅ Action-oriented language
- ✅ Clean, parseable formatting

## Usage Instructions

### For Different Job Applications:

**Startup/Product Companies**: Use the main ATS resume or LLM Developer version
**Large Corporations/MNCs**: Use the Team Lead version emphasizing leadership
**Data-Focused Companies**: Use the Data Scientist version
**AI Research Labs**: Use the LLM Developer version with research emphasis

### Customization Tips:

1. **Keywords**: Add specific keywords from job descriptions
2. **Company Focus**: Emphasize relevant projects (healthcare, finance, etc.)
3. **Role Level**: Adjust leadership emphasis based on seniority level
4. **Technical Stack**: Highlight technologies mentioned in job requirements

## File Formats

- **Markdown (.md)**: For easy editing and version control
- **Plain Text (.txt)**: Maximum ATS compatibility
- **Recommended**: Convert to PDF using clean, professional formatting

## Contact Information

- **Phone**: +91-**********
- **Email**: <EMAIL>
- **LinkedIn**: https://www.linkedin.com/in/om-prakash-sonkar-1a0a79114/
- **GitHub**: https://github.com/ommnnitald

## Key Strengths Highlighted

- **Leadership**: Team management, project delivery, mentorship
- **Technical Expertise**: AI/ML, LLMs, computer vision, NLP
- **Business Impact**: Quantified results, client satisfaction, efficiency improvements
- **Industry Experience**: Healthcare, regulatory compliance, industrial IoT
- **Certifications**: Relevant technical and professional certifications

## Next Steps

1. Choose the appropriate resume version for your target role
2. Customize keywords based on specific job descriptions
3. Convert to PDF with professional formatting
4. Ensure all contact information is current
5. Tailor the professional summary for specific applications

---

*Last Updated: January 2025*
*Resume optimized for ATS systems and AI/ML industry standards*
